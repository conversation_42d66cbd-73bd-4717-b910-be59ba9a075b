using Application.Apis.Clients.Response.Instrument;
using Domain.Enums;
using Geometry.Core;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using Workflow.StabilityAnalysis.Classes;

namespace Workflow.Extensions;

public static class DxfFileExtensions
{
    /// <summary>
    /// Retrieves instrument points from a given DXF file based on the water-related layers.
    /// Instrument points include polylines, lightweight polylines, lines, and circles
    /// that are associated with water-related keywords in their layers.
    /// </summary>
    /// <param name="dxfFile">The DXF file to extract instrument points from.</param>
    /// <param name="section">The section information used to process the instrument points.</param>
    /// <returns>
    /// An <see cref="InstrumentPoints"/> object containing water level indicators and piezometers
    /// extracted from the DXF file.
    /// </returns>
    public static InstrumentPoints GetInstrumentPoints(
        this DxfFile dxfFile,
        SectionInfo section)
    {
        var entities = dxfFile.Entities
            .Where(x => x.Layer.ContainsWaterKeyword())
            .ToList();

        var instruments = new List<Instrument>();

        foreach (var entity in entities)
        {
            if (entity.EntityType == DxfEntityType.Polyline)
            {
                var polyline = (DxfPolyline)entity;

                foreach (var vertex in polyline.Vertices)
                {
                    instruments.Add(new Instrument()
                    {
                        PointD = new PointD(vertex.Location.X,
                            vertex.Location.Y),
                        DryReading = false
                    });
                }
            }
            else if (entity.EntityType == DxfEntityType.LwPolyline)
            {
                var lwPolyline = (DxfLwPolyline)entity;

                foreach (var vertex in lwPolyline.Vertices)
                {
                    instruments.Add(new Instrument()
                    {
                        PointD = new PointD(vertex.X, vertex.Y),
                        DryReading = false
                    });
                }
            }
            else if (entity.EntityType == DxfEntityType.Line)
            {
                var line = (DxfLine)entity;

                instruments.Add(new Instrument()
                {
                    PointD = new PointD(line.P1.X, line.P1.Y),
                    DryReading = false
                });

                instruments.Add(new Instrument()
                {
                    PointD = new PointD(line.P2.X, line.P2.Y),
                    DryReading = false
                });
            }
            else if (entity.EntityType == DxfEntityType.Circle)
            {
                var circle = (DxfCircle)entity;
                instruments.Add(new Instrument()
                {
                    PointD = new PointD(circle.Center.X, circle.Center.Y),
                    DryReading = false
                });
            }
        }

        return new InstrumentPoints()
        {
            WaterLevelIndicators = instruments,
            Piezometers = new()
        };
    }

    /// <summary>
    /// Retrieves instrument points from a given DXF file based on the provided section information
    /// and a list of instrument details.
    /// Instrument points include water level indicators and piezometers, with their coordinates
    /// adjusted based on the section's coordinate system and skew settings.
    /// </summary>
    /// <param name="dxfFile">The DXF file to extract instrument points from.</param>
    /// <param name="section">The section information used to process and adjust the instrument points.</param>
    /// <param name="instruments">A list of instrument details used to map and process the instrument points.</param>
    /// <returns>
    /// An <see cref="InstrumentPoints"/> object containing water level indicators and piezometers
    /// extracted and adjusted based on the section's settings.
    /// </returns>
    public static InstrumentPoints GetInstrumentPoints(
        this DxfFile dxfFile,
        SectionInfo section,
        List<GetInstrumentByIdResponse> instruments)
    {
        var piezometers = new List<Instrument>();
        var waterLevelIndicators = new List<Instrument>();
        var upstreamCoordinate =
            Dxf.Core.Helper.GetFirstPointOfSectionUpstream(dxfFile);
        var separateInstruments =
            section.SectionReviewData.SectionReview.StructureType.Activities
                .Any(x =>
                    x.Activity == Domain.Enums.Activity.SeparateInstruments);

        foreach (var instrument in section.Instruments)
        {
            var instrumentInfo =
                instruments.First(i => i.Id == instrument.InstrumentId);
            
            var sectionCoordinates = section.Coordinates;

            if (instrumentInfo.CoordinateSetting.Datum !=
                sectionCoordinates.Datum)
            {
                instrumentInfo.CoordinateSetting.Systems.Utm =
                    Coordinate.Core.Helper.ToDatum(
                        instrumentInfo.CoordinateSetting.Datum,
                        sectionCoordinates.Datum,
                        instrumentInfo.CoordinateSetting.Systems.Utm);
            }

            var XCoordinate = sectionCoordinates.UpstreamCoordinateSetting
                .CoordinateSystems.Utm.ToPointD();
            var YCoordinate = sectionCoordinates.DownstreamCoordinateSetting
                .CoordinateSystems.Utm.ToPointD();

            var point = new PointD()
            {
                X = section.IsSkew
                    ? Geometry.Core.Helper.GetInstrumentXPoint(
                        upstreamCoordinate, XCoordinate, YCoordinate,
                        sectionCoordinates.MidpointCoordinateSetting
                            .CoordinateSystems.Utm.ToPointD(),
                        instrumentInfo.CoordinateSetting.Systems.Utm.ToPointD())
                    : Geometry.Core.Helper.GetInstrumentXPoint(
                        upstreamCoordinate, XCoordinate, YCoordinate,
                        instrumentInfo.CoordinateSetting.Systems.Utm
                            .ToPointD()),
                Y = (double)(instrument.ReadingQuota ?? 0)
            };

            switch (instrumentInfo.Type)
            {
                case InstrumentType.WaterLevelIndicator:
                    waterLevelIndicators.Add(new()
                    {
                        PointD = point,
                        InstrumentInfo = instrumentInfo,
                        DryReading = instrument.DryReading
                    });
                    break;
                case InstrumentType.ElectricPiezometer:
                case InstrumentType.OpenStandpipePiezometer:
                {
                    var existingPiezometer = piezometers.FirstOrDefault(x =>
                        x.InstrumentInfo.Id == instrument.InstrumentId);

                    if (existingPiezometer != null &&
                        (double?)instrument.ReadingQuota >
                        existingPiezometer.PointD.Y)
                    {
                        piezometers.Remove(existingPiezometer);
                        piezometers.Add(new()
                        {
                            PointD = point,
                            InstrumentInfo = instrumentInfo,
                            DryReading = instrument.DryReading
                        });
                    }
                    else if (existingPiezometer == null)
                    {
                        piezometers.Add(new()
                        {
                            PointD = point,
                            InstrumentInfo = instrumentInfo,
                            DryReading = instrument.DryReading
                        });
                    }
                }

                    break;
            }
        }

        return new InstrumentPoints()
        {
            Piezometers = piezometers,
            WaterLevelIndicators = waterLevelIndicators
        };
    }

    /// <summary>
    /// Extracts slope limit points from a DXF file by identifying circles in layers 
    /// that contain slope limit-related keywords. These points are used to define 
    /// slope constraints in stability analysis.
    /// </summary>
    /// <param name="dxfFile">The DXF file to extract slope limit points from.</param>
    /// <returns>
    /// A list of <see cref="PointD"/> objects representing the slope limit points 
    /// extracted from the DXF file. Returns an empty list if no slope limits are defined.
    /// </returns>
    public static List<PointD> GetSlopeLimits(this DxfFile dxfFile)
    {
        var slopeLimits = new List<PointD>();
        
        var hasLimitsDefined =
            dxfFile.Layers.Any(layer => layer.Name.ContainsLimitsKeyword())
            && dxfFile.Entities.Any(x => x.Layer.ContainsLimitsKeyword());

        if (!hasLimitsDefined)
        {
            return slopeLimits;
        }

        var entitiesWithLimits = dxfFile.Entities
            .Where(entity =>
                entity.Layer.ContainsLimitsKeyword() &&
                entity.EntityType == DxfEntityType.Circle)
            .Select(entity => (DxfCircle)entity)
            .OrderBy(circle => circle.Center.X)
            .ToList();

        slopeLimits.AddRange(
            entitiesWithLimits.Select(entity => new PointD(entity.Center.X, entity.Center.Y))
        );

        return slopeLimits;
    }
}