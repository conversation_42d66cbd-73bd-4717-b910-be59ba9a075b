using Application.Apis.Clients;
using Dxf.Core.Extensions;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Geometry.Core;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using Serilog;
using Slide.Core;
using Slide.Core.Objects.Sli;
using System.Text;
using TriangleNet.Geometry;
using TriangleNet.Meshing;
using static Workflow.Constants.SectionWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Section.Activities
{
    public class CreateInitialSliFileActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public CreateInitialSliFileActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var sectionId = (context.GetTransientVariable<Domain.Messages.Commands.Section.CreateSliFile>(Variables.Command)).SectionId;

                var section = await _clientsApiService
                    .GetSectionById(sectionId);

                if (section == null)
                {
                    return Done();
                }

                foreach (var sectionReview in section.Reviews)
                {
                    if (sectionReview.ConstructionStages.Any())
                    {
                        foreach (var constructionStage in sectionReview.ConstructionStages)
                        {
                            var constructionStageSli = MountSliFile(constructionStage.Drawing);

                            if (constructionStageSli == null)
                            {
                                continue;
                            }

                            var result = await _clientsApiService.AddSliToSectionReview(new()
                            {
                                SectionId = sectionId,
                                ReviewId = sectionReview.Id,
                                ConstructionStageId = constructionStage.Id,
                                Sli = constructionStageSli
                            });

                            if (!result.IsSuccessStatusCode)
                            {
                                Log.Error("Error adding SLI to construction stage.");
                                return Fault("Error adding SLI to construction stage.");
                            }
                        }

                        continue;
                    }

                    var reviewSli = MountSliFile(sectionReview.Drawing);

                    if (reviewSli == null)
                    {
                        continue;
                    }

                    var resultSectionReview = await _clientsApiService.AddSliToSectionReview(new()
                    {
                        SectionId = sectionId,
                        ReviewId = sectionReview.Id,
                        Sli = reviewSli
                    });

                    if (!resultSectionReview.IsSuccessStatusCode)
                    {
                        Log.Error("Error adding SLI to section review.");
                        return Fault("Error adding SLI to section review.");
                    }
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in CreateInitialSliFileActivity");
                return Fault(e);
            }
        }

        private Application.Apis._Shared.File MountSliFile(Application.Apis._Shared.File dxfFile)
        {
            if (dxfFile == null || string.IsNullOrEmpty(dxfFile.Base64))
            {
                return null;
            }

            var dxf = new DxfFile();
            dxf = dxf.LoadWithBase64(dxfFile.Base64);

            var externalEntities = dxf.Entities.Where(e => e.Layer.ToLower() == "external").ToList();
            var materialEntities = dxf.Entities.Where(e => e.Layer.ToLower() == "material").ToList();

            var externalVertices = externalEntities
                .Where(x => x.EntityType == DxfEntityType.LwPolyline)
                .Select(x => (DxfLwPolyline)x)
                .SelectMany(x => x.Vertices)
                .Select(x => new Vertice()
                {
                    X = x.X,
                    Y = x.Y
                })
                .ToList();

            externalVertices.AddRange(externalEntities
                .Where(x => x.EntityType == DxfEntityType.Polyline)
                .Select(x => (DxfPolyline)x)
                .SelectMany(x => x.Vertices)
                .Select(x => new Vertice()
                {
                    X = x.Location.X,
                    Y = x.Location.Y
                })
                .ToList());

            externalVertices.AddRange(externalEntities
                .Where(x => x.EntityType == DxfEntityType.Line)
                .SelectMany(x => new[]
                {
                            new Vertice()
                            {
                                X = ((DxfLine)x).P1.X,
                                Y = ((DxfLine)x).P1.Y
                            },
                            new Vertice()
                            {
                                X = ((DxfLine)x).P2.X,
                                Y = ((DxfLine)x).P2.Y
                            },
                })
                .ToList());

            if (externalVertices.Count == 0)
            {
                Log.Information("No external vertices found in the DXF file.");
                return null;
            }

            // Send the first vertice to the end of the list
            externalVertices = externalVertices.Skip(1).Concat(externalVertices.Take(1)).ToList();

            var materials = new List<(Vertice, int)>();
            var groupIndex = 1;

            foreach (var entity in materialEntities)
            {
                if (entity.EntityType == DxfEntityType.LwPolyline)
                {
                    var polyline = (DxfLwPolyline)entity;

                    foreach (var vertex in polyline.Vertices)
                    {
                        materials.Add((new Vertice()
                        {
                            X = vertex.X,
                            Y = vertex.Y
                        }, groupIndex));
                    }

                    groupIndex++;
                }

                if (entity.EntityType == DxfEntityType.Polyline)
                {
                    var polyline = (DxfPolyline)entity;

                    foreach (var vertex in polyline.Vertices)
                    {
                        materials.Add((new Vertice()
                        {
                            X = vertex.Location.X,
                            Y = vertex.Location.Y
                        }, groupIndex));
                    }

                    groupIndex++;
                }

                if (entity.EntityType == DxfEntityType.Line)
                {
                    var line = (DxfLine)entity;

                    materials.Add((new Vertice()
                    {
                        X = line.P1.X,
                        Y = line.P1.Y
                    }, groupIndex));

                    materials.Add((new Vertice()
                    {
                        X = line.P2.X,
                        Y = line.P2.Y
                    }, groupIndex));

                    groupIndex++;
                }
            }

            if (materials.Count == 0)
            {
                Log.Information("No materials found in the DXF file.");
                return null;
            }

            var polygon = new Polygon();

            polygon.Add(new Contour(externalVertices.Select(x => new Vertex(x.X, x.Y, 1)), 1));

            materials
                .GroupBy(x => x.Item2)
                .SelectMany(group => group.Take(group.Count() - 1)
                .Zip(group.Skip(1), (element1, element2) =>
                new
                {
                    Element1 = element1.Item1,
                    Element2 = element2.Item1
                })
                .Where(data => data.Element1 != null || data.Element2 != null)
                .Select(data =>
                new
                {
                    Vertices = new[] { data.Element1, data.Element2 }
                        .Where(el => el != null)
                        .Select(el => new Vertex(el.X, el.Y))
                        .ToArray()
                }))
                .ToList()
                .ForEach(data =>
                {
                    if (data.Vertices.Length > 1)
                    {
                        polygon.Add(new Segment(data.Vertices[0], data.Vertices[1], 1), 1);
                    }

                    foreach (Vertex vertex in data.Vertices)
                    {
                        polygon.Points.Add(vertex);
                    }
                });

            var options = new ConstraintOptions()
            {
                ConformingDelaunay = true,
                Convex = false,
                SegmentSplitting = 2
            };

            var mesh = polygon.Triangulate(options);

            var sliFile = new SliFile();

            sliFile.AddVertices(externalVertices.Select(x => new PointD(x.X, x.Y)).ToList());
            sliFile.AddVertices(materials.Select(x => new PointD(x.Item1.X, x.Item1.Y)).ToList());

            foreach (var triangle in mesh.Triangles)
            {
                var vertice1 = triangle.GetVertex(0);
                var vertice2 = triangle.GetVertex(1);
                var vertice3 = triangle.GetVertex(2);

                var sliVertice1 = sliFile.Vertices.FirstOrDefault(x => x.X == vertice1.X && x.Y == vertice1.Y);

                if (sliVertice1 == null)
                {
                    var newVertice = new PointD(vertice1.X, vertice1.Y);
                    sliVertice1 = sliFile.AddVertice(newVertice);
                }

                var sliVertice2 = sliFile.Vertices.FirstOrDefault(x => x.X == vertice2.X && x.Y == vertice2.Y);

                if (sliVertice2 == null)
                {
                    var newVertice = new PointD(vertice2.X, vertice2.Y);
                    sliVertice2 = sliFile.AddVertice(newVertice);
                }

                var sliVertice3 = sliFile.Vertices.FirstOrDefault(x => x.X == vertice3.X && x.Y == vertice3.Y);

                if (sliVertice3 == null)
                {
                    var newVertice = new PointD(vertice3.X, vertice3.Y);
                    sliVertice3 = sliFile.AddVertice(newVertice);
                }

                sliFile.AddCell(new()
                {
                    Vertice1 = (int)sliVertice1.Index,
                    Vertice2 = (int)sliVertice2.Index,
                    Vertice3 = (int)sliVertice3.Index,
                    Material = "soil1"
                });
            }

            var externalVerticesIndex = externalVertices
                .Select(x => (int)sliFile.Vertices.First(v => v.X == x.X && v.Y == x.Y).Index)
                .Distinct()
                .ToList();

            sliFile.AddExterior(externalVerticesIndex);

            var minX = externalVertices.Min(x => x.X);
            var maxX = externalVertices.Max(x => x.X);

            var vertexWithSmalledXAndLargestY = externalVertices.Where(x => x.X == minX).OrderByDescending(x => x.Y).First();
            var vertexWithLargestXAndLargestY = externalVertices.Where(x => x.X == maxX).OrderByDescending(x => x.Y).First();

            var upperVertices = externalVertices
                .Where(x => x.X > vertexWithSmalledXAndLargestY.X && x.X < vertexWithLargestXAndLargestY.X && (x.Y >= vertexWithSmalledXAndLargestY.Y || x.Y >= vertexWithLargestXAndLargestY.Y)
                || (x.X == vertexWithSmalledXAndLargestY.X && x.Y == vertexWithSmalledXAndLargestY.Y || x.X == vertexWithLargestXAndLargestY.X && x.Y == vertexWithLargestXAndLargestY.Y))
                .OrderBy(x => x.X)
                .ToList();

            var upperVerticesIndex = upperVertices
                .Select(x => (int)sliFile.Vertices.First(v => v.X == x.X && v.Y == x.Y).Index)
                .Distinct()
                .ToList();

            sliFile.AddSlope(upperVerticesIndex);

            upperVertices = upperVertices.OrderBy(x => x.X).ToList();

            var upperVerticeWithMinX = upperVertices.First();
            var upperVerticeWithMaxX = upperVertices.Last();

            sliFile.AddSlopeLimits(upperVerticeWithMinX, upperVerticeWithMaxX);

            var lastExternalVertice = externalVertices.Last();

            sliFile.AddGeometryInfos(externalVertices.Select(x => new GeometryInfo()
            {
                X = x.X,
                Y = x.Y,
                MaterialType = 0,
                EndFlag = x.X == lastExternalVertice.X && x.Y == lastExternalVertice.Y && x.Index == lastExternalVertice.Index ? true : false,
            }).ToList());

            foreach (var group in materials.GroupBy(x => x.Item2))
            {
                for (int i = 0; i < group.Count(); i++)
                {
                    var element1 = group.ElementAtOrDefault(i).Item1;
                    var element2 = group.ElementAtOrDefault(i + 1).Item1;

                    if (element1 != null && element2 != null)
                    {
                        sliFile.AddGeometryInfo(new()
                        {
                            X = element1.X,
                            Y = element1.Y,
                            MaterialType = 3,
                            EndFlag = false
                        });
                    }
                    else
                    {
                        sliFile.AddGeometryInfo(new()
                        {
                            X = element1.X,
                            Y = element1.Y,
                            MaterialType = 3,
                            EndFlag = true
                        });
                    }
                }
            }

            sliFile.AddMaterialProperties(new()
            {
                Name = "Support 1",
                Red = 0,
                Green = 0,
                Blue = 255,
                Guid = Guid.NewGuid()
            });

            sliFile.AddMaterialProperties(new()
            {
                Name = "Support 2",
                Red = 0,
                Green = 255,
                Blue = 0,
                Guid = Guid.NewGuid()
            });

            var textBytes = Encoding.UTF8.GetBytes(sliFile.Save());

            return new()
            {
                Base64 = Convert.ToBase64String(textBytes),
                Name = $"{DateTime.Now.ToString("ddMMyyyy_HHmmss")}.sli"
            };
        }
    }
}
